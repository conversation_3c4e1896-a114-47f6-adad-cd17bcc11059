{
    "version": "0.2.0",
    "configurations": [
        { 
            "name": "Modula 18 - Start", 
            "type": "python", 
            "request": "launch",
            "stopOnEntry": "false",
            "console": "integratedTerminal", 
            "program": "/home/<USER>/odoo/itms/modula18/odoo/odoo-bin", 
            "args": [ 
                "-c", 
                "/home/<USER>/odoo/itms/modula18/.vscode/modula.conf",
                "--dev=xml",
                "-d", 
                "modula_db_1_5", 
            ],

            "cwd": "/home/<USER>/odoo/itms/modula18", 
        }, 
        { 
            "name": "Modula 18 - Upgrade", 
            "type": "python", 
            "request": "launch", 
            "stopOnEntry": "false",
            "console": "integratedTerminal", 
            "program": "/home/<USER>/odoo/itms/modula18/odoo/odoo-bin", 
            "args": [ 
                "-c", 
                "/home/<USER>/odoo/itms/modula18/.vscode/modula.conf",
                "-u",
                "modula_sale",
                "-d", 
                "modula_db_1_5",
            ],
            "cwd": "/home/<USER>/odoo/itms/modula18", 
        }, 
        { 
            "name": "Modula 18 - Tests", 
            "type": "python", 
            "request": "launch",
            # "GEVENT_SUPPORT": True,
            "stopOnEntry": "false", 
            "program": "/home/<USER>/odoo/itms/modula18/odoo/odoo-bin", 
            "args": [ 
                "-c", 
                "/home/<USER>/odoo/itms/modula18/.vscode/modula.conf",
                "-i", 
                "modula_downpayment", 
                "-d", 
                "modula_db_1_5", 
                "--test-enable", 
                "--stop-after-init", 
            ], 
            "cwd": "/home/<USER>/odoo/itms/modula18", 
        },
    ]
}